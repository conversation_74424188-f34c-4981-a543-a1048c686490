"""
Modal configuration and setup for the AI Podcast Clipper.
"""

import modal
from fastapi.security import HTTPBearer

# Modal image configuration
image = (modal.Image.from_registry(
    "nvidia/cuda:12.4.0-devel-ubuntu22.04", add_python="3.12")
    .apt_install(["ffmpeg", "libgl1-mesa-glx", "wget", "libcudnn8", "libcudnn8-dev"])
    .pip_install_from_requirements("requirements.txt")
    .run_commands(["mkdir -p /usr/share/fonts/truetype/custom", 
                   "wget -O /usr/share/fonts/truetype/custom/Anton-Regular.ttf https://github.com/google/fonts/raw/main/ofl/anton/Anton-Regular.ttf", 
                   "fc-cache -f -v"])
    .add_local_dir("asd", "/asd", copy=True))

# Modal app instance
app = modal.App("ai-podcast-clipper", image=image)

# Volume for model cache
volume = modal.Volume.from_name(
    "ai-podcast-clipper-model-cache", create_if_missing=True
)

# Mount path for torch cache
mount_path = "/root/.cache/torch"

# Authentication scheme
auth_scheme = HTTPBearer()
