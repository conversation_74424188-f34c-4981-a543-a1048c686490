"""
Main application class for the AI Podcast Clipper.
"""

import json
import os
import pathlib
import shutil
import uuid
import boto3
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials

from .modal_config import app, auth_scheme, mount_path, volume
from .models import ProcessVideoRequest
from .transcription_service import TranscriptionService
from .ai_services import GeminiService
from .clip_processor import process_clip

import modal


@app.cls(gpu="L40S", timeout=900, retries=0, scaledown_window=20, 
         secrets=[modal.Secret.from_name("ai-podcast-clipper-secret")], 
         volumes={mount_path: volume})
class AiPodcastClipper:
    """Main application class for processing podcast videos."""
    
    @modal.enter()
    def load_model(self):
        """Load all required models and services."""
        print("Loading models")
        
        # Initialize transcription service
        self.transcription_service = TranscriptionService()
        self.transcription_service.load_models()
        
        # Initialize AI service
        self.ai_service = GeminiService()
        self.ai_service.initialize_client()

    @modal.fastapi_endpoint(method="POST")
    def process_video(self, request: ProcessVideoRequest, 
                     token: HTTPAuthorizationCredentials = Depends(auth_scheme)):
        """
        Process a video to create clips.
        
        Args:
            request: Video processing request
            token: Authorization token
            
        Returns:
            Processing result
        """
        s3_key = request.s3_key

        # Validate authentication
        if token.credentials != os.environ["AUTH_TOKEN"]:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, 
                                detail="Incorrect bearer token", 
                                headers={"WWW-Authenticate": "Bearer"})
        
        # Setup working directory
        run_id = str(uuid.uuid4())
        base_dir = pathlib.Path("/tmp") / run_id
        base_dir.mkdir(parents=True, exist_ok=True)

        try:
            # Download video file
            video_path = base_dir / "input.mp4"
            s3_client = boto3.client("s3")
            s3_client.download_file("ai-podcast-clipper-br", s3_key, str(video_path))

            # 1. Transcription 
            transcript_segments_json = self.transcription_service.transcribe_video(base_dir, video_path)
            transcript_segments = json.loads(transcript_segments_json)

            # 2. Identify moments for clips
            print("Identifying clip moments")
            identified_moments_raw = self.ai_service.identify_moments(transcript_segments)

            # Clean and parse AI response
            cleaned_json_string = identified_moments_raw.strip()
            if cleaned_json_string.startswith("```json"):
                cleaned_json_string = cleaned_json_string[len("```json"):].strip()
            if cleaned_json_string.endswith("```"):
                cleaned_json_string = cleaned_json_string[:-len("```")].strip()
            
            clip_moments = json.loads(cleaned_json_string)
            if not clip_moments or not isinstance(clip_moments, list):
                print("Error: Identified moments is not a list")
                clip_moments = []

            print(clip_moments)

            # 3. Process clips
            for index, moment in enumerate(clip_moments[:1]):
                if "start" in moment and "end" in moment:
                    print(f"Processing clip {index} from {moment['start']} to {moment['end']}")
                    process_clip(base_dir, video_path, s3_key,
                                 moment["start"], moment["end"], index, transcript_segments)

        finally:
            # Cleanup
            if base_dir.exists():
                print(f"Cleaning up temp dir after {base_dir}")
                shutil.rmtree(base_dir, ignore_errors=True)
        
        return {"status": "completed", "clips_processed": len(clip_moments[:1])}
