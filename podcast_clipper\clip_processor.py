"""
Clip processing utilities for creating individual video clips.
"""

import os
import pickle
import shutil
import subprocess
import time
import boto3
from .video_processor import create_vertical_video
from .subtitle_processor import create_subtitles_with_ffmpeg, create_viral_subtitles_with_background


def process_clip(base_dir: str, original_video_path: str, s3_key: str, 
                start_time: float, end_time: float, clip_index: int, 
                transcript_segments: list):
    """
    Process a single video clip from the original video.
    
    Args:
        base_dir: Base directory for processing
        original_video_path: Path to the original video file
        s3_key: S3 key for the original video
        start_time: Start time of the clip in seconds
        end_time: End time of the clip in seconds
        clip_index: Index of the clip
        transcript_segments: List of transcript segments
    """
    clip_name = f"clip_{clip_index}"
    s3_key_dir = os.path.dirname(s3_key)
    output_s3_key = f"{s3_key_dir}/{clip_name}.mp4"
    print(f"Output S3 key: {output_s3_key}")

    clip_dir = base_dir / clip_name
    clip_dir.mkdir(parents=True, exist_ok=True)

    clip_segment_path = clip_dir / f"{clip_name}_segment.mp4"
    vertical_mp4_path = clip_dir / "pyavi" / "video_out_vertical.mp4"
    subtitle_output_path = clip_dir / "pyavi" / "video_with_subtitles.mp4"

    (clip_dir / "pywork").mkdir(exist_ok=True)
    pyframes_path = clip_dir / "pyframes"
    pyavi_path = clip_dir / "pyavi"
    audio_path = clip_dir / "pyavi" / "audio.wav"

    pyframes_path.mkdir(exist_ok=True)
    pyavi_path.mkdir(exist_ok=True)

    # Extract clip segment
    duration = end_time - start_time
    cut_command = (f"ffmpeg -i {original_video_path} -ss {start_time} -t {duration} "
                   f"{clip_segment_path}")
    subprocess.run(cut_command, shell=True, check=True, capture_output=True, text=True)
    
    # Extract audio
    extract_cmd = f"ffmpeg -i {clip_segment_path} -vn -acodec pcm_s16le -ar 16000 -ac 1 {audio_path}"
    subprocess.run(extract_cmd, shell=True, check=True, capture_output=True)
    
    # Copy clip for Columbia processing
    shutil.copy(clip_segment_path, base_dir / f"{clip_name}.mp4")

    # Run Columbia face tracking
    columbia_command = (f"python Columbia_test.py --videoName {clip_name} "
                        f"--videoFolder {str(base_dir)} "
                        f"--pretrainModel weight/finetuning_TalkSet.model")

    columbia_start_time = time.time()
    subprocess.run(columbia_command, cwd="/asd", shell=True)
    columbia_end_time = time.time()
    print(f"Columbia script completed in {columbia_end_time - columbia_start_time:.2f} seconds")

    # Load tracking results
    tracks_path = clip_dir / "pywork" / "tracks.pckl"
    scores_path = clip_dir / "pywork" / "scores.pckl"
    if not tracks_path.exists() or not scores_path.exists():
        raise FileNotFoundError("Tracks or scores not found for clip")

    with open(tracks_path, "rb") as f:
        tracks = pickle.load(f)

    with open(scores_path, "rb") as f:
        scores = pickle.load(f)

    # Create vertical video
    cvv_start_time = time.time()
    create_vertical_video(
        tracks, scores, pyframes_path, pyavi_path, audio_path, vertical_mp4_path
    )
    cvv_end_time = time.time()
    print(f"Clip {clip_index} vertical video creation time: {cvv_end_time - cvv_start_time:.2f} seconds")

    # Add subtitles - usando estilo viral
    create_viral_subtitles_with_background(transcript_segments, start_time,
                                         end_time, vertical_mp4_path, subtitle_output_path, max_words=3)

    # Upload to S3
    s3_client = boto3.client("s3")
    s3_client.upload_file(
        subtitle_output_path, "ai-podcast-clipper-br", output_s3_key)
