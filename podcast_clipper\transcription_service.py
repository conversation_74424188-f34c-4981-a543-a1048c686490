"""
Transcription service using WhisperX for audio processing.
"""

import json
import subprocess
import time
import whisperx


class TranscriptionService:
    """Service for transcribing audio using WhisperX."""
    
    def __init__(self):
        """Initialize the transcription service."""
        self.whisperx_model = None
        self.alignment_model = None
        self.metadata = None
    
    def load_models(self):
        """Load WhisperX models for transcription and alignment."""
        print("Loading transcription models...")
        
        self.whisperx_model = whisperx.load_model(
            "large-v2", device="cuda", compute_type="float16")
        
        self.alignment_model, self.metadata = whisperx.load_align_model(
            language_code="en", 
            device="cuda"
        )
        
        print("Transcription models loaded...")
    
    def transcribe_video(self, base_dir: str, video_path: str) -> str:
        """
        Transcribe a video file and return word-level segments.
        
        Args:
            base_dir: Base directory for temporary files
            video_path: Path to the video file
            
        Returns:
            JSON string containing word segments with timing
        """
        audio_path = base_dir / "audio.wav"
        extract_cmd = f"ffmpeg -i {video_path} -vn -acodec pcm_s16le -ar 16000 -ac 1 {audio_path}"
        subprocess.run(extract_cmd, shell=True, check=True, capture_output=True)

        print("Starting transcription with WhisperX...")
        start_time = time.time()

        audio = whisperx.load_audio(str(audio_path))
        result = self.whisperx_model.transcribe(audio, batch_size=16)

        result = whisperx.align(
            result["segments"], 
            self.alignment_model, 
            self.metadata, 
            audio, 
            device="cuda",
            return_char_alignments=False
        )

        duration = time.time() - start_time
        print(f"Transcription and alignment took {duration} seconds")

        segments = []

        if "word_segments" in result:
            for word_segment in result["word_segments"]:
                segments.append({
                    "start": word_segment["start"],
                    "end": word_segment["end"],
                    "word": word_segment["word"],
                })
        
        return json.dumps(segments)
