"""
Subtitle processing utilities for creating video subtitles.
"""

import os
import subprocess
import pysubs2


def create_viral_subtitles_with_background(transcript_segments: list, clip_start: float, clip_end: float,
                                         clip_video_path: str, output_path: str, max_words: int = 3):
    """
    Create viral-style subtitles with background boxes using FFmpeg drawtext filter.
    This creates the TikTok/Instagram viral style with background boxes.
    """
    temp_dir = os.path.dirname(output_path)

    clip_segments = [segment for segment in transcript_segments
                     if segment.get("start") is not None
                     and segment.get("end") is not None
                     and segment.get("end") > clip_start
                     and segment.get("start") < clip_end]

    subtitles = []
    current_words = []
    current_start = None
    current_end = None

    for segment in clip_segments:
        word = segment.get("word", "").strip()
        seg_start = segment.get("start")
        seg_end = segment.get("end")

        if not word or seg_start is None or seg_end is None:
            continue

        start_rel = max(0.0, seg_start - clip_start)
        end_rel = max(0.0, seg_end - clip_start)

        if end_rel <= 0:
            continue

        if not current_words:
            current_start = start_rel
            current_end = end_rel
            current_words = [word]
        elif len(current_words) >= max_words:
            subtitles.append((current_start, current_end, ' '.join(current_words)))
            current_words = [word]
            current_start = start_rel
            current_end = end_rel
        else:
            current_words.append(word)
            current_end = end_rel

    if current_words:
        subtitles.append((current_start, current_end, ' '.join(current_words)))

    # Criar filtros FFmpeg para cada legenda
    drawtext_filters = []

    for start, end, text in subtitles:
        # Texto em maiúscula para estilo viral
        text = text.upper().replace("'", "\\'").replace('"', '\\"')

        # Filtro com fundo semi-transparente
        filter_str = (
            f"drawtext=text='{text}'"
            f":fontfile='C\\:/Windows/Fonts/arial.ttf'"
            f":fontsize=60"
            f":fontcolor=white"
            f":borderw=3"
            f":bordercolor=black"
            f":box=1"
            f":boxcolor=black@0.7"
            f":boxborderw=10"
            f":x=(w-text_w)/2"
            f":y=h*0.75"
            f":enable='between(t,{start},{end})'"
        )
        drawtext_filters.append(filter_str)

    # Combinar todos os filtros
    if drawtext_filters:
        video_filter = ",".join(drawtext_filters)
        ffmpeg_cmd = (
            f"ffmpeg -y -i {clip_video_path} "
            f"-vf \"{video_filter}\" "
            f"-c:v h264 -preset fast -crf 23 -c:a copy "
            f"{output_path}"
        )
    else:
        # Se não há legendas, apenas copiar o vídeo
        ffmpeg_cmd = f"ffmpeg -y -i {clip_video_path} -c copy {output_path}"

    subprocess.run(ffmpeg_cmd, shell=True, check=True)


def create_subtitles_with_ffmpeg(transcript_segments: list, clip_start: float, clip_end: float,
                                clip_video_path: str, output_path: str, max_words: int = 3,
                                style: str = "viral"):
    """
    Create subtitles for a video clip using FFmpeg.

    Args:
        transcript_segments: List of transcript segments with timing
        clip_start: Start time of the clip
        clip_end: End time of the clip
        clip_video_path: Path to the input video
        output_path: Path for the output video with subtitles
        max_words: Maximum words per subtitle line
        style: Subtitle style ('viral' for TikTok/viral style, 'classic' for original)
    """
    temp_dir = os.path.dirname(output_path)
    subtitle_path = os.path.join(temp_dir, "temp_subtitles.ass")

    clip_segments = [segment for segment in transcript_segments
                     if segment.get("start") is not None
                     and segment.get("end") is not None
                     and segment.get("end") > clip_start
                     and segment.get("start") < clip_end
                    ]

    subtitles = []
    current_words = []
    current_start = None
    current_end = None

    for segment in clip_segments:
        word = segment.get("word", "").strip()
        seg_start = segment.get("start")
        seg_end = segment.get("end")

        if not word or seg_start is None or seg_end is None:
            continue

        start_rel = max(0.0, seg_start - clip_start)
        end_rel = max(0.0, seg_end - clip_start)

        if end_rel <= 0:
            continue

        if not current_words:
            current_start = start_rel
            current_end = end_rel
            current_words = [word]
        elif len(current_words) >= max_words:
            subtitles.append(
                (current_start, current_end, ' '.join(current_words)))
            current_words = [word]
            current_start = start_rel
            current_end = end_rel
        else:
            current_words.append(word)
            current_end = end_rel

    if current_words:
        subtitles.append(
            (current_start, current_end, ' '.join(current_words)))

    subs = pysubs2.SSAFile()

    subs.info["WrapStyle"] = 0
    subs.info["ScaledBorderAndShadow"] = "yes"
    subs.info["PlayResX"] = 1080
    subs.info["PlayResY"] = 1920
    subs.info["ScriptType"] = "v4.00+"

    if style == "viral":
        # Estilo viral TikTok/Instagram
        style_name = "Viral"
        new_style = pysubs2.SSAStyle()
        new_style.fontname = "Arial Black"  # Fonte mais impactante
        new_style.fontsize = 180  # Maior para mais impacto
        new_style.primarycolor = pysubs2.Color(255, 255, 255)  # Branco
        new_style.outline = 8.0  # Contorno mais grosso
        new_style.outlinecolor = pysubs2.Color(0, 0, 0)  # Contorno preto
        new_style.shadow = 0  # Sem sombra para look mais limpo
        new_style.alignment = 5  # Centro da tela
        new_style.marginl = 80
        new_style.marginr = 80
        new_style.marginv = 300  # Posição mais central
        new_style.spacing = 2.0
        new_style.bold = True  # Negrito para mais impacto

        # Estilo para palavras destacadas (verde neon)
        highlight_style = pysubs2.SSAStyle()
        highlight_style.fontname = "Arial Black"
        highlight_style.fontsize = 200  # Ainda maior para destaque
        highlight_style.primarycolor = pysubs2.Color(0, 255, 0)  # Verde neon
        highlight_style.outline = 8.0
        highlight_style.outlinecolor = pysubs2.Color(0, 0, 0)
        highlight_style.shadow = 0
        highlight_style.alignment = 5
        highlight_style.marginl = 80
        highlight_style.marginr = 80
        highlight_style.marginv = 300
        highlight_style.spacing = 2.0
        highlight_style.bold = True

        subs.styles[style_name] = new_style
        subs.styles["Highlight"] = highlight_style
    else:
        # Estilo clássico original
        style_name = "Default"
        new_style = pysubs2.SSAStyle()
        new_style.fontname = "Anton"
        new_style.fontsize = 140
        new_style.primarycolor = pysubs2.Color(255, 255, 255)
        new_style.outline = 2.0
        new_style.shadow = 2.0
        new_style.shadowcolor = pysubs2.Color(0, 0, 0, 128)
        new_style.alignment = 2
        new_style.marginl = 50
        new_style.marginr = 50
        new_style.marginv = 50
        new_style.spacing = 0.0

        subs.styles[style_name] = new_style

    # Palavras-chave que devem ser destacadas em verde
    highlight_keywords = [
        'não', 'nunca', 'sempre', 'muito', 'super', 'mega', 'ultra',
        'incrível', 'impressionante', 'chocante', 'surpreendente',
        'segredo', 'verdade', 'mentira', 'fake', 'real', 'sério',
        'importante', 'cuidado', 'atenção', 'olha', 'veja', 'escuta'
    ]

    for start, end, text in subtitles:
        start_time = pysubs2.make_time(s=start)
        end_time = pysubs2.make_time(s=end)

        if style == "viral":
            # Converter para maiúscula para estilo viral
            text = text.upper()

            # Destacar palavras-chave em verde
            formatted_text = text
            for keyword in highlight_keywords:
                keyword_upper = keyword.upper()
                if keyword_upper in text:
                    # Usar tags ASS para mudar o estilo
                    formatted_text = formatted_text.replace(
                        keyword_upper,
                        f"{{\\r\\c&H00FF00&\\b1}}{keyword_upper}{{\\r}}"
                    )

            line = pysubs2.SSAEvent(
                start=start_time, end=end_time, text=formatted_text, style=style_name)
        else:
            line = pysubs2.SSAEvent(
                start=start_time, end=end_time, text=text, style=style_name)

        subs.events.append(line)

    subs.save(subtitle_path)

    ffmpeg_cmd = (f"ffmpeg -y -i {clip_video_path} -vf \"ass={subtitle_path}\" "
                  f"-c:v h264 -preset fast -crf 23 {output_path}")

    subprocess.run(ffmpeg_cmd, shell=True, check=True)
