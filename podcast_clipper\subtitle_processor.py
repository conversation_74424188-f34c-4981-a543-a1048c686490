"""
Subtitle processing utilities for creating video subtitles.
"""

import os
import subprocess
import pysubs2


def create_subtitles_with_ffmpeg(transcript_segments: list, clip_start: float, clip_end: float,
                                clip_video_path: str, output_path: str, max_words: int = 3):
    """
    Create subtitles for a video clip using FFmpeg.

    Args:
        transcript_segments: List of transcript segments with timing
        clip_start: Start time of the clip
        clip_end: End time of the clip
        clip_video_path: Path to the input video
        output_path: Path for the output video with subtitles
        max_words: Maximum words per subtitle line
    """
    temp_dir = os.path.dirname(output_path)
    subtitle_path = os.path.join(temp_dir, "temp_subtitles.ass")

    clip_segments = [segment for segment in transcript_segments
                     if segment.get("start") is not None
                     and segment.get("end") is not None
                     and segment.get("end") > clip_start
                     and segment.get("start") < clip_end
                    ]

    subtitles = []
    current_words = []
    current_start = None
    current_end = None

    for segment in clip_segments:
        word = segment.get("word", "").strip()
        seg_start = segment.get("start")
        seg_end = segment.get("end")

        if not word or seg_start is None or seg_end is None:
            continue

        start_rel = max(0.0, seg_start - clip_start)
        end_rel = max(0.0, seg_end - clip_start)

        if end_rel <= 0:
            continue

        if not current_words:
            current_start = start_rel
            current_end = end_rel
            current_words = [word]
        elif len(current_words) >= max_words:
            subtitles.append(
                (current_start, current_end, ' '.join(current_words)))
            current_words = [word]
            current_start = start_rel
            current_end = end_rel
        else:
            current_words.append(word)
            current_end = end_rel

    if current_words:
        subtitles.append(
            (current_start, current_end, ' '.join(current_words)))

    subs = pysubs2.SSAFile()

    subs.info["WrapStyle"] = 0
    subs.info["ScaledBorderAndShadow"] = "yes"
    subs.info["PlayResX"] = 1080
    subs.info["PlayResY"] = 1920
    subs.info["ScriptType"] = "v4.00+"

    # Estilo viral TikTok/Instagram
    style_name = "Viral"
    new_style = pysubs2.SSAStyle()
    new_style.fontname = "Arial Black"  # Fonte mais impactante
    new_style.fontsize = 180  # Maior para mais impacto
    new_style.primarycolor = pysubs2.Color(255, 255, 255)  # Branco
    new_style.outline = 8.0  # Contorno mais grosso
    new_style.outlinecolor = pysubs2.Color(0, 0, 0)  # Contorno preto
    new_style.shadow = 0  # Sem sombra para look mais limpo
    new_style.alignment = 5  # Centro da tela
    new_style.marginl = 80
    new_style.marginr = 80
    new_style.marginv = 300  # Posição mais central
    new_style.spacing = 2.0
    new_style.bold = True  # Negrito para mais impacto

    subs.styles[style_name] = new_style

    for start, end, text in subtitles:
        start_time = pysubs2.make_time(s=start)
        end_time = pysubs2.make_time(s=end)

        # Converter para maiúscula para estilo viral
        text = text.upper()

        line = pysubs2.SSAEvent(
            start=start_time, end=end_time, text=text, style=style_name)
        subs.events.append(line)

    subs.save(subtitle_path)

    ffmpeg_cmd = (f"ffmpeg -y -i {clip_video_path} -vf \"ass={subtitle_path}\" "
                  f"-c:v h264 -preset fast -crf 23 {output_path}")

    subprocess.run(ffmpeg_cmd, shell=True, check=True)
