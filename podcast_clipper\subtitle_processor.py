"""
Subtitle processing utilities for creating video subtitles.
"""

import os
import subprocess
import pysubs2

def create_subtitles_with_ffmpeg(transcript_segments: list, clip_start: float, clip_end: float, clip_video_path: str, output_path: str, max_words: int = 5, karaoke_style: bool = True):
    temp_dir = os.path.dirname(output_path)
    subtitle_path = os.path.join(temp_dir, "temp_subtitles.ass")

    clip_segments = [segment for segment in transcript_segments
                     if segment.get("start") is not None
                     and segment.get("end") is not None
                     and segment.get("end") > clip_start
                     and segment.get("start") < clip_end
                     ]

    subtitles = []

    if karaoke_style:
        # Estilo Karaoke: cada palavra é uma legenda individual
        for segment in clip_segments:
            word = segment.get("word", "").strip()
            seg_start = segment.get("start")
            seg_end = segment.get("end")

            if not word or seg_start is None or seg_end is None:
                continue

            start_rel = max(0.0, seg_start - clip_start)
            end_rel = max(0.0, seg_end - clip_start)

            if end_rel <= 0:
                continue

            subtitles.append((start_rel, end_rel, word))
    else:
        # Estilo original: agrupa palavras
        current_words = []
        current_start = None
        current_end = None

        for segment in clip_segments:
            word = segment.get("word", "").strip()
            seg_start = segment.get("start")
            seg_end = segment.get("end")

            if not word or seg_start is None or seg_end is None:
                continue

            start_rel = max(0.0, seg_start - clip_start)
            end_rel = max(0.0, seg_end - clip_start)

            if end_rel <= 0:
                continue

            if not current_words:
                current_start = start_rel
                current_end = end_rel
                current_words = [word]
            elif len(current_words) >= max_words:
                subtitles.append(
                    (current_start, current_end, ' '.join(current_words)))
                current_words = [word]
                current_start = start_rel
                current_end = end_rel
            else:
                current_words.append(word)
                current_end = end_rel

        if current_words:
            subtitles.append(
                (current_start, current_end, ' '.join(current_words)))

    subs = pysubs2.SSAFile()

    subs.info["WrapStyle"] = 0
    subs.info["ScaledBorderAndShadow"] = "yes"
    subs.info["PlayResX"] = 1080
    subs.info["PlayResY"] = 1920
    subs.info["ScriptType"] = "v4.00+"

    if karaoke_style:
        # Estilo Karaoke OpusClip
        style_name = "KaraokeDefault"
        new_style = pysubs2.SSAStyle()
        new_style.fontname = "Montserrat"
        new_style.fontsize = 40
        new_style.primarycolor = pysubs2.Color(0, 0, 0)  # Black text
        new_style.outline = 8.0  # 8px stroke
        new_style.outlinecolor = pysubs2.Color(0, 0, 0)  # #000000FF stroke
        new_style.shadow = 2.0  # Shadow blur
        new_style.shadowcolor = pysubs2.Color(0, 0, 0)  # #000000FF shadow
        new_style.alignment = 2  # Bottom center
        new_style.marginl = 50
        new_style.marginr = 50
        new_style.marginv = 100  # Mais espaço na parte inferior
        new_style.spacing = 0.0
        new_style.bold = True  # Para simular o peso Black da fonte

        # Estilo para palavra destacada (highlighted)
        highlight_style_name = "KaraokeHighlight"
        highlight_style = pysubs2.SSAStyle()
        highlight_style.fontname = "Montserrat"
        highlight_style.fontsize = 40
        highlight_style.primarycolor = pysubs2.Color(4, 248, 39)  # #04f827FF verde brilhante
        highlight_style.outline = 8.0
        highlight_style.outlinecolor = pysubs2.Color(0, 0, 0)
        highlight_style.shadow = 2.0
        highlight_style.shadowcolor = pysubs2.Color(0, 0, 0)
        highlight_style.alignment = 2
        highlight_style.marginl = 50
        highlight_style.marginr = 50
        highlight_style.marginv = 100
        highlight_style.spacing = 0.0
        highlight_style.bold = True

        subs.styles[style_name] = new_style
        subs.styles[highlight_style_name] = highlight_style
    else:
        # Estilo original
        style_name = "Default"
        new_style = pysubs2.SSAStyle()
        new_style.fontname = "Anton"
        new_style.fontsize = 140
        new_style.primarycolor = pysubs2.Color(255, 255, 255)
        new_style.outline = 2.0
        new_style.shadow = 2.0
        new_style.shadowcolor = pysubs2.Color(0, 0, 0, 128)
        new_style.alignment = 2
        new_style.marginl = 50
        new_style.marginr = 50
        new_style.marginv = 50
        new_style.spacing = 0.0

        subs.styles[style_name] = new_style

    if karaoke_style:
        # Implementar estilo Karaoke com highlighting palavra por palavra
        # Criar contexto de 3 linhas (Three lines como no OpusClip)

        for i, (start, end, word) in enumerate(subtitles):
            start_time = pysubs2.make_time(s=start)
            end_time = pysubs2.make_time(s=end)

            # Converter palavra para uppercase (como no OpusClip)
            word_upper = word.upper()

            # Criar contexto de 3 linhas (palavra anterior, atual, próxima)
            context_words = []

            # Palavra anterior (se existir) - cor normal (preta)
            if i > 0:
                prev_word = subtitles[i-1][2].upper()
                context_words.append(prev_word)

            # Palavra atual (destacada) - cor verde #04f827FF
            context_words.append(f"{{\\c&H27F804&}}{word_upper}{{\\c&H000000&}}")

            # Palavra seguinte (se existir) - cor normal (preta)
            if i < len(subtitles) - 1:
                next_word = subtitles[i+1][2].upper()
                context_words.append(next_word)

            # Criar texto com quebras de linha (Three lines)
            text_content = "\\N".join(context_words)

            line = pysubs2.SSAEvent(
                start=start_time, end=end_time, text=text_content, style="KaraokeDefault")
            subs.events.append(line)
    else:
        # Estilo original
        for i, (start, end, text) in enumerate(subtitles):
            start_time = pysubs2.make_time(s=start)
            end_time = pysubs2.make_time(s=end)
            line = pysubs2.SSAEvent(
                start=start_time, end=end_time, text=text, style=style_name)
            subs.events.append(line)

    subs.save(subtitle_path)

    ffmpeg_cmd = (f"ffmpeg -y -i {clip_video_path} -vf \"ass={subtitle_path}\" "
                  f"-c:v h264 -preset fast -crf 23 {output_path}")

    subprocess.run(ffmpeg_cmd, shell=True, check=True)