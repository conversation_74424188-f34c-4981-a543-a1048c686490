"""
AI services for content analysis using Google Gemini.
"""

import os
from google import genai


class GeminiService:
    """Service for AI-powered content analysis using Google Gemini."""
    
    def __init__(self):
        """Initialize the Gemini service."""
        self.gemini_client = None
    
    def initialize_client(self):
        """Initialize the Gemini client."""
        print("Creating gemini client...")
        self.gemini_client = genai.Client(api_key=os.environ["GEMINI_API_KEY"])
        print("Created gemini client...")
    
    def identify_moments(self, transcript: dict) -> str:
        """
        Identify interesting moments in a transcript for clip creation.
        
        Args:
            transcript: Dictionary containing transcript data
            
        Returns:
            String containing AI response with identified moments
        """
        response = self.gemini_client.models.generate_content(model="gemini-2.5-flash", contents=""" 
    This is a podcast video transcript consisting of word, along with each words's start and end time. I am looking to create clips between a minimum of 30 and maximum of 60 seconds long. The clip should never exceed 60 seconds.

    Your task is to find and extract stories, or question and their corresponding answers from the transcript.
    Each clip should begin with the question and conclude with the answer.
    It is acceptable for the clip to include a few additional sentences before a question if it aids in contextualizing the question.

    Please adhere to the following rules:
    - Ensure that clips do not overlap with one another.
    - Start and end timestamps of the clips should align perfectly with the sentence boundaries in the transcript.
    - Only use the start and end timestamps provided in the input. modifying timestamps is not allowed.
    - Format the output as a list of JSON objects, each representing a clip with 'start' and 'end' timestamps: [{"start": seconds, "end": seconds}, ...clip2, clip3]. The output should always be readable by the python json.loads function.
    - Aim to generate longer clips between 40-60 seconds, and ensure to include as much content from the context as viable.

    Avoid including:
    - Moments of greeting, thanking, or saying goodbye.
    - Non-question and answer interactions.   

    If there are no valid clips to extract, the output should be an empty list [], in JSON format. Also readable by json.loads() in Python.
                   
    The transcript is as follows:\n\n""" + str(transcript))
        print(f"Identified moments response: {response.text}")
        return response.text
